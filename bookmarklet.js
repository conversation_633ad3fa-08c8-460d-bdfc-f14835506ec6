/**
 * <PERSON><PERSON>sent Remover Bookmarklet
 * 
 * Create a bookmark with this JavaScript code as the URL to instantly
 * remove cookie consent banners on any website.
 * 
 * To use:
 * 1. Create a new bookmark
 * 2. Set the URL to the code below (starting with javascript:)
 * 3. Click the bookmark on any page with cookie banners
 */

javascript:(function(){
  // Remove common cookie consent elements
  document.querySelectorAll('[class*="cookie"], [id*="cookie"], [class*="consent"], [id*="consent"], [class*="gdpr"], [id*="gdpr"], [class*="privacy"], [id*="privacy"], .cc-banner, .cc-window, .optanon-alert-box-wrapper, .cky-consent-container, .cookie-law-info-bar, .moove_gdpr_cookie_info_bar').forEach(el => el.remove());
  
  // Remove fixed position high z-index elements
  document.querySelectorAll('*').forEach(el => {
    const style = window.getComputedStyle(el);
    if (style.position === 'fixed' && parseInt(style.zIndex) > 1000 && el.textContent && /cookie|consent|privacy|gdpr|accept|decline/i.test(el.textContent)) {
      el.remove();
    }
  });
  
  // Remove overlays
  document.querySelectorAll('.modal-backdrop, .overlay, .backdrop, [class*="overlay"], [class*="backdrop"]').forEach(el => {
    const style = window.getComputedStyle(el);
    if (style.position === 'fixed' && parseInt(style.zIndex) > 1000) {
      el.remove();
    }
  });
  
  // Reset body overflow
  document.body.style.overflow = '';
  document.documentElement.style.overflow = '';
  
  // Try to click accept buttons
  document.querySelectorAll('button, a, div').forEach(el => {
    if (el.textContent && /^(accept|ok|got it|continue|agree)$/i.test(el.textContent.trim())) {
      el.click();
    }
  });
  
  alert('Cookie consent banners removed!');
})();