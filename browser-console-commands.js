/**
 * Browser Console Commands for Cookie Consent Removal
 * 
 * Copy and paste these commands directly into your browser's developer console
 * to remove cookie consent banners manually.
 */

// === QUICK REMOVAL COMMANDS ===

// 1. Remove common cookie banners
document.querySelectorAll('[class*="cookie"], [id*="cookie"], [class*="consent"], [id*="consent"], [class*="gdpr"], [id*="gdpr"]').forEach(el => el.remove());

// 2. Remove fixed position elements with high z-index (likely popups)
document.querySelectorAll('*').forEach(el => {
  const style = window.getComputedStyle(el);
  if (style.position === 'fixed' && parseInt(style.zIndex) > 1000) {
    el.remove();
  }
});

// 3. Force click accept buttons
document.querySelectorAll('button, a, div').forEach(el => {
  if (el.textContent && /accept|ok|got it|continue|agree/i.test(el.textContent.toLowerCase())) {
    el.click();
  }
});

// 4. Remove modal backdrops and overlays
document.querySelectorAll('.modal-backdrop, .overlay, .backdrop, [class*="overlay"], [class*="backdrop"]').forEach(el => el.remove());

// 5. Reset body overflow
document.body.style.overflow = '';
document.documentElement.style.overflow = '';

// === COMPREHENSIVE REMOVAL FUNCTION ===

function removeCookieBanners() {
  // Common selectors
  const selectors = [
    '[class*="cookie"]', '[id*="cookie"]',
    '[class*="consent"]', '[id*="consent"]',
    '[class*="gdpr"]', '[id*="gdpr"]',
    '[class*="privacy"]', '[id*="privacy"]',
    '.cc-banner', '.cc-window',
    '.optanon-alert-box-wrapper',
    '.cky-consent-container',
    '.cookie-law-info-bar',
    '.moove_gdpr_cookie_info_bar'
  ];
  
  let removed = 0;
  selectors.forEach(selector => {
    document.querySelectorAll(selector).forEach(el => {
      el.remove();
      removed++;
    });
  });
  
  // Remove by text content
  document.querySelectorAll('*').forEach(el => {
    if (el.textContent && /cookie|consent|privacy|gdpr/i.test(el.textContent) && 
        /accept|decline|agree/i.test(el.textContent) && 
        el.textContent.split(' ').length < 100) {
      el.remove();
      removed++;
    }
  });
  
  console.log(`Removed ${removed} cookie consent elements`);
  return removed;
}

// Run the function
removeCookieBanners();

// === PERSISTENT MONITORING ===

// Set up automatic removal every 2 seconds
setInterval(() => {
  removeCookieBanners();
}, 2000);

console.log('Cookie banner removal activated. Running every 2 seconds.');