import React from 'react';
import { Zap, MapPin, Mail, Phone, Linkedin, Twitter } from 'lucide-react';

const Footer: React.FC = () => {
  const links = {
    company: [
      { name: 'About Us', href: '#' },
      { name: 'How It Works', href: '#how-it-works' },
      { name: 'Technology', href: '#technology' },
      { name: 'Benefits', href: '#benefits' },
    ],
    legal: [
      { name: 'Privacy Policy', href: '#' },
      { name: 'Terms of Service', href: '#' },
      { name: 'Cookie Policy', href: '#' },
      { name: 'Compliance', href: '#' },
    ],
    support: [
      { name: 'Contact Us', href: '#contact' },
      { name: 'Documentation', href: '#' },
      { name: 'FAQ', href: '#' },
      { name: 'Support Center', href: '#' },
    ],
  };

  return (
    <footer className="bg-gray-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8">
          {/* Company Info */}
          <div className="lg:col-span-2">
            <div className="flex items-center space-x-2 mb-6">
              <div className="bg-primary-900 p-2 rounded-lg">
                <Zap className="h-6 -6 text-secondary-500" />
              </div>
              <span className="text-xl font-bold">Energy Live Exchange</span>
            </div>
            <p className="text-gray-300 mb-6 max-w-md">
              UAE's first blockchain-powered renewable energy marketplace, revolutionizing how we trade and consume clean energy.
            </p>
            <div className="space-y-3">
              <div className="flex items-center text-gray-300">
                <MapPin className="h-5 w-5 mr-3 text-primary-900" />
                SRTIP Freezone, Sharjah, UAE
              </div>
              <div className="flex items-center text-gray-300">
                <Mail className="h-5 w-5 mr-3 text-primary-900" />
                <EMAIL>
              </div>
              <div className="flex items-center text-gray-300">
                <Phone className="h-5 w-5 mr-3 text-primary-900" />
                +971-*********
              </div>
            </div>
          </div>

          {/* Links Sections */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Company</h3>
            <ul className="space-y-3">
              {links.company.map((link) => (
                <li key={link.name}>
                  <a
                    href={link.href}
                    className="text-gray-300 hover:text-primary-900 transition-colors duration-200"
                  >
                    {link.name}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          <div>
            <h3 className="text-lg font-semibold mb-4">Legal</h3>
            <ul className="space-y-3">
              {links.legal.map((link) => (
                <li key={link.name}>
                  <a
                    href={link.href}
                    className="text-gray-300 hover:text-primary-900 transition-colors duration-200"
                  >
                    {link.name}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          <div>
            <h3 className="text-lg font-semibold mb-4">Support</h3>
            <ul className="space-y-3">
              {links.support.map((link) => (
                <li key={link.name}>
                  <a
                    href={link.href}
                    className="text-gray-300 hover:text-primary-900 transition-colors duration-200"
                  >
                    {link.name}
                  </a>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Partnership Section */}
        <div className="mt-12 pt-8 border-t border-gray-800">
          <div className="bg-secondary-500/10 rounded-xl p-6 border border-secondary-500/20">
            <h3 className="text-lg font-semibold mb-3">Powered by SRTIP Freezone</h3>
            <p className="text-gray-300 text-sm">
              Proudly supported by Sharjah Research, Technology and Innovation Park (SRTIP), 
              fostering innovation in renewable energy and blockchain technology.
            </p>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="mt-12 pt-8 border-t border-gray-800 flex flex-col md:flex-row justify-between items-center">
          <div className="text-gray-400 text-sm mb-4 md:mb-0">
            © 2025 Energy Live Exchange. All rights reserved.
          </div>
          
          {/* Social Links */}
          <div className="flex space-x-4">
            <a
              href="#"
              className="p-2 rounded-lg bg-gray-800 hover:bg-gray-700 transition-colors duration-200"
            >
              <Linkedin className="h-5 w-5 text-gray-300 hover:text-white" />
            </a>
            <a
              href="#"
              className="p-2 rounded-lg bg-gray-800 hover:bg-gray-700 transition-colors duration-200"
            >
              <Twitter className="h-5 w-5 text-gray-300 hover:text-white" />
            </a>
          </div>
        </div>

        {/* Cookie Consent Banner */}
        <div className="fixed bottom-4 left-4 right-4 max-w-sm mx-auto bg-gray-800 border border-gray-700 rounded-lg p-4 shadow-lg z-50">
          <p className="text-sm text-gray-300 mb-3">
            We use cookies to enhance your experience and analyze site usage.
          </p>
          <div className="flex space-x-2">
            <button className="flex-1 bg-primary-900 hover:bg-primary-950 text-secondary-500 text-sm py-2 px-3 rounded transition-colors duration-200">
              Accept
            </button>
            <button className="flex-1 bg-gray-700 hover:bg-gray-600 text-white text-sm py-2 px-3 rounded transition-colors duration-200">
              Decline
            </button>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;