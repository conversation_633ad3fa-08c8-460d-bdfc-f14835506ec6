import React from 'react';

const ColorShowcase: React.FC = () => {
  const colorPalette = {
    primary: [
      { name: 'Primary 50', value: '#f0f9ff', rgb: 'rgb(240, 249, 255)', hsl: 'hsl(204, 100%, 97%)' },
      { name: 'Primary 100', value: '#e0f2fe', rgb: 'rgb(224, 242, 254)', hsl: 'hsl(204, 96%, 94%)' },
      { name: 'Primary 200', value: '#bae6fd', rgb: 'rgb(186, 230, 253)', hsl: 'hsl(204, 94%, 86%)' },
      { name: 'Primary 300', value: '#7dd3fc', rgb: 'rgb(125, 211, 252)', hsl: 'hsl(204, 94%, 74%)' },
      { name: 'Primary 400', value: '#38bdf8', rgb: 'rgb(56, 189, 248)', hsl: 'hsl(204, 93%, 60%)' },
      { name: 'Primary 500', value: '#0ea5e9', rgb: 'rgb(14, 165, 233)', hsl: 'hsl(204, 89%, 48%)' },
      { name: 'Primary 600', value: '#0284c7', rgb: 'rgb(2, 132, 199)', hsl: 'hsl(204, 98%, 39%)' },
      { name: 'Primary 700', value: '#0369a1', rgb: 'rgb(3, 105, 161)', hsl: 'hsl(204, 96%, 32%)' },
      { name: 'Primary 800', value: '#075985', rgb: 'rgb(7, 89, 133)', hsl: 'hsl(204, 90%, 27%)' },
      { name: 'Primary 900', value: '#002a35', rgb: 'rgb(0, 42, 53)', hsl: 'hsl(192, 100%, 10%)', main: true },
      { name: 'Primary 950', value: '#001a21', rgb: 'rgb(0, 26, 33)', hsl: 'hsl(193, 100%, 6%)' },
    ],
    secondary: [
      { name: 'Secondary 50', value: '#fefce8', rgb: 'rgb(254, 252, 232)', hsl: 'hsl(55, 92%, 95%)' },
      { name: 'Secondary 100', value: '#fef9c3', rgb: 'rgb(254, 249, 195)', hsl: 'hsl(55, 96%, 88%)' },
      { name: 'Secondary 200', value: '#fef08a', rgb: 'rgb(254, 240, 138)', hsl: 'hsl(53, 98%, 77%)' },
      { name: 'Secondary 300', value: '#fde047', rgb: 'rgb(253, 224, 71)', hsl: 'hsl(50, 98%, 64%)' },
      { name: 'Secondary 400', value: '#facc15', rgb: 'rgb(250, 204, 21)', hsl: 'hsl(48, 96%, 53%)' },
      { name: 'Secondary 500', value: '#e6ff2b', rgb: 'rgb(230, 255, 43)', hsl: 'hsl(67, 100%, 58%)', main: true },
      { name: 'Secondary 600', value: '#ca8a04', rgb: 'rgb(202, 138, 4)', hsl: 'hsl(41, 96%, 40%)' },
      { name: 'Secondary 700', value: '#a16207', rgb: 'rgb(161, 98, 7)', hsl: 'hsl(36, 92%, 33%)' },
      { name: 'Secondary 800', value: '#854d0e', rgb: 'rgb(133, 77, 14)', hsl: 'hsl(32, 81%, 29%)' },
      { name: 'Secondary 900', value: '#713f12', rgb: 'rgb(113, 63, 18)', hsl: 'hsl(28, 73%, 26%)' },
      { name: 'Secondary 950', value: '#422006', rgb: 'rgb(66, 32, 6)', hsl: 'hsl(26, 83%, 14%)' },
    ],
    neutrals: [
      { name: 'Neutral 50', value: '#f8fafc', rgb: 'rgb(248, 250, 252)', hsl: 'hsl(210, 20%, 98%)' },
      { name: 'Neutral 100', value: '#f1f5f9', rgb: 'rgb(241, 245, 249)', hsl: 'hsl(210, 40%, 96%)' },
      { name: 'Neutral 200', value: '#e2e8f0', rgb: 'rgb(226, 232, 240)', hsl: 'hsl(214, 32%, 91%)' },
      { name: 'Neutral 300', value: '#cbd5e1', rgb: 'rgb(203, 213, 225)', hsl: 'hsl(215, 25%, 84%)' },
      { name: 'Neutral 400', value: '#94a3b8', rgb: 'rgb(148, 163, 184)', hsl: 'hsl(215, 20%, 65%)' },
      { name: 'Neutral 500', value: '#64748b', rgb: 'rgb(100, 116, 139)', hsl: 'hsl(215, 16%, 47%)' },
      { name: 'Neutral 600', value: '#475569', rgb: 'rgb(71, 85, 105)', hsl: 'hsl(215, 19%, 35%)' },
      { name: 'Neutral 700', value: '#334155', rgb: 'rgb(51, 65, 85)', hsl: 'hsl(215, 25%, 27%)' },
      { name: 'Neutral 800', value: '#1e293b', rgb: 'rgb(30, 41, 59)', hsl: 'hsl(217, 33%, 17%)' },
      { name: 'Neutral 900', value: '#0f172a', rgb: 'rgb(15, 23, 42)', hsl: 'hsl(222, 47%, 11%)' },
      { name: 'Neutral 950', value: '#020617', rgb: 'rgb(2, 6, 23)', hsl: 'hsl(222, 84%, 5%)' },
    ],
    accents: [
      { name: 'Teal 400', value: '#2dd4bf', rgb: 'rgb(45, 212, 191)', hsl: 'hsl(172, 66%, 50%)' },
      { name: 'Teal 500', value: '#14b8a6', rgb: 'rgb(20, 184, 166)', hsl: 'hsl(172, 80%, 40%)' },
      { name: 'Emerald 400', value: '#34d399', rgb: 'rgb(52, 211, 153)', hsl: 'hsl(156, 64%, 52%)' },
      { name: 'Emerald 500', value: '#10b981', rgb: 'rgb(16, 185, 129)', hsl: 'hsl(156, 84%, 39%)' },
      { name: 'Orange 400', value: '#fb923c', rgb: 'rgb(251, 146, 60)', hsl: 'hsl(27, 96%, 61%)' },
      { name: 'Orange 500', value: '#f97316', rgb: 'rgb(249, 115, 22)', hsl: 'hsl(25, 95%, 53%)' },
      { name: 'Red 400', value: '#f87171', rgb: 'rgb(248, 113, 113)', hsl: 'hsl(0, 91%, 71%)' },
      { name: 'Red 500', value: '#ef4444', rgb: 'rgb(239, 68, 68)', hsl: 'hsl(0, 84%, 60%)' },
    ],
  };

  const combinations = [
    { name: 'Primary CTA', bg: '#002a35', text: '#f8fafc', contrast: '15.8:1' },
    { name: 'Secondary CTA', bg: '#e6ff2b', text: '#002a35', contrast: '12.4:1' },
    { name: 'Light Background', bg: '#f8fafc', text: '#002a35', contrast: '15.8:1' },
    { name: 'Dark Background', bg: '#002a35', text: '#f8fafc', contrast: '15.8:1' },
    { name: 'Success State', bg: '#dcfce7', text: '#15803d', contrast: '7.2:1' },
    { name: 'Warning State', bg: '#fef3c7', text: '#d97706', contrast: '6.8:1' },
    { name: 'Error State', bg: '#fee2e2', text: '#b91c1c', contrast: '8.1:1' },
    { name: 'Info State', bg: '#dbeafe', text: '#1d4ed8', contrast: '7.5:1' },
  ];

  const ColorSwatch = ({ color }: { color: any }) => (
    <div className="group cursor-pointer">
      <div 
        className="w-full h-20 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 mb-2 transition-transform group-hover:scale-105"
        style={{ backgroundColor: color.value }}
      ></div>
      <div className="text-xs">
        <div className="font-semibold text-gray-900 dark:text-white mb-1">
          {color.name} {color.main && '★'}
        </div>
        <div className="text-gray-600 dark:text-gray-400 space-y-1">
          <div>{color.value}</div>
          <div>{color.rgb}</div>
          <div>{color.hsl}</div>
        </div>
      </div>
    </div>
  );

  const CombinationCard = ({ combo }: { combo: any }) => (
    <div 
      className="p-6 rounded-lg border border-gray-200 dark:border-gray-700 transition-transform hover:scale-105"
      style={{ backgroundColor: combo.bg, color: combo.text }}
    >
      <div className="font-semibold mb-2">{combo.name}</div>
      <div className="text-sm opacity-90 mb-3">
        Sample text to demonstrate readability and contrast
      </div>
      <div className="text-xs opacity-75">
        Contrast: {combo.contrast} ✓ WCAG AA
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
            Energy Live Exchange Color System
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300">
            Complete color palette based on #002a35 and #e6ff2b
          </p>
        </div>

        {/* Primary Colors */}
        <section className="mb-12">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
            Primary Colors - Deep Navy Blue
          </h2>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
            {colorPalette.primary.map((color, index) => (
              <ColorSwatch key={index} color={color} />
            ))}
          </div>
        </section>

        {/* Secondary Colors */}
        <section className="mb-12">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
            Secondary Colors - Bright Yellow-Green
          </h2>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
            {colorPalette.secondary.map((color, index) => (
              <ColorSwatch key={index} color={color} />
            ))}
          </div>
        </section>

        {/* Neutral Colors */}
        <section className="mb-12">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
            Neutral Colors - Blue-tinted Grays
          </h2>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
            {colorPalette.neutrals.map((color, index) => (
              <ColorSwatch key={index} color={color} />
            ))}
          </div>
        </section>

        {/* Accent Colors */}
        <section className="mb-12">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
            Accent Colors - Complementary Palette
          </h2>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-4">
            {colorPalette.accents.map((color, index) => (
              <ColorSwatch key={index} color={color} />
            ))}
          </div>
        </section>

        {/* Color Combinations */}
        <section className="mb-12">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
            Accessible Color Combinations
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {combinations.map((combo, index) => (
              <CombinationCard key={index} combo={combo} />
            ))}
          </div>
        </section>

        {/* Interactive Examples */}
        <section className="mb-12">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
            Interactive Elements
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="space-y-4">
              <h3 className="font-semibold text-gray-900 dark:text-white">Primary CTAs</h3>
              <button className="w-full py-3 px-6 bg-[#002a35] text-white rounded-lg hover:bg-[#075985] transition-all duration-200 transform hover:scale-105">
                Primary Button
              </button>
              <button className="w-full py-3 px-6 bg-[#e6ff2b] text-[#002a35] rounded-lg hover:bg-[#facc15] transition-all duration-200 transform hover:scale-105">
                Secondary Button
              </button>
            </div>
            
            <div className="space-y-4">
              <h3 className="font-semibold text-gray-900 dark:text-white">Outline Buttons</h3>
              <button className="w-full py-3 px-6 border-2 border-[#002a35] text-[#002a35] rounded-lg hover:bg-[#002a35] hover:text-white transition-all duration-200">
                Outline Primary
              </button>
              <button className="w-full py-3 px-6 border-2 border-[#e6ff2b] text-[#e6ff2b] rounded-lg hover:bg-[#e6ff2b] hover:text-[#002a35] transition-all duration-200">
                Outline Secondary
              </button>
            </div>

            <div className="space-y-4">
              <h3 className="font-semibold text-gray-900 dark:text-white">Status States</h3>
              <div className="p-3 bg-[#dcfce7] text-[#15803d] rounded-lg border-l-4 border-[#16a34a]">
                Success Message
              </div>
              <div className="p-3 bg-[#fef3c7] text-[#d97706] rounded-lg border-l-4 border-[#f59e0b]">
                Warning Message
              </div>
              <div className="p-3 bg-[#fee2e2] text-[#b91c1c] rounded-lg border-l-4 border-[#dc2626]">
                Error Message
              </div>
            </div>

            <div className="space-y-4">
              <h3 className="font-semibold text-gray-900 dark:text-white">Disabled States</h3>
              <button className="w-full py-3 px-6 bg-gray-300 text-gray-500 rounded-lg cursor-not-allowed opacity-60" disabled>
                Disabled Button
              </button>
              <input 
                type="text" 
                placeholder="Disabled input" 
                className="w-full py-3 px-4 bg-gray-100 text-gray-400 border border-gray-300 rounded-lg cursor-not-allowed" 
                disabled 
              />
            </div>
          </div>
        </section>

        {/* Usage Guidelines */}
        <section className="bg-white dark:bg-gray-800 rounded-xl p-8 border border-gray-200 dark:border-gray-700">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
            Usage Guidelines
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Primary Color (#002a35)
              </h3>
              <ul className="space-y-2 text-gray-600 dark:text-gray-300">
                <li>• Main navigation and headers</li>
                <li>• Primary call-to-action buttons</li>
                <li>• Important text and headings</li>
                <li>• Brand elements and logos</li>
                <li>• Dark mode backgrounds</li>
              </ul>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Secondary Color (#e6ff2b)
              </h3>
              <ul className="space-y-2 text-gray-600 dark:text-gray-300">
                <li>• Accent elements and highlights</li>
                <li>• Secondary call-to-action buttons</li>
                <li>• Energy-related visualizations</li>
                <li>• Success states and positive feedback</li>
                <li>• Interactive hover states</li>
              </ul>
            </div>
          </div>
          
          <div className="mt-8 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
            <h4 className="font-semibold text-blue-900 dark:text-blue-300 mb-2">
              Accessibility Note
            </h4>
            <p className="text-blue-800 dark:text-blue-300 text-sm">
              All color combinations shown meet WCAG 2.1 AA standards with a minimum contrast ratio of 4.5:1 for normal text and 3:1 for large text. Always test color combinations in your specific context.
            </p>
          </div>
        </section>
      </div>
    </div>
  );
};

export default ColorShowcase;