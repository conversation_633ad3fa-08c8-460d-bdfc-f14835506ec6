import React from 'react';
import { DollarSign, Leaf, Users, TrendingUp, Shield, Clock } from 'lucide-react';

const Benefits: React.FC = () => {
  const benefits = [
    {
      icon: DollarSign,
      title: 'Transparent Pricing',
      description: 'Fair market rates with no hidden fees or intermediary markups',
    },
    {
      icon: Clock,
      title: 'Automated Settlements',
      description: 'Instant payments through smart contracts with zero delays',
    },
    {
      icon: Leaf,
      title: 'Energy Traceability',
      description: 'Track your energy source from production to consumption',
    },
    {
      icon: Users,
      title: 'Community Empowerment',
      description: 'Support local energy producers and build sustainable communities',
    },
    {
      icon: Shield,
      title: 'Security & Trust',
      description: 'Blockchain-secured transactions with immutable records',
    },
    {
      icon: TrendingUp,
      title: 'Environmental Impact',
      description: 'Reduce carbon footprint with verified renewable energy',
    },
  ];

  const stats = [
    { number: '85%', label: 'Cost Reduction', description: 'Average savings on energy bills' },
    { number: '24/7', label: 'Trading Hours', description: 'Continuous energy marketplace' },
    { number: '100%', label: 'Renewable', description: 'Clean energy guaranteed' },
    { number: '0', label: 'Intermediaries', description: 'Direct peer-to-peer trading' },
  ];

  return (
    <section id="benefits" className="py-20 bg-gray-50 dark:bg-gray-800">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
            Why Choose ELX?
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Experience the benefits of blockchain-powered renewable energy trading
          </p>
        </div>

        {/* Stats Section */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-16">
          {stats.map((stat, index) => (
            <div key={index} className="text-center">
              <div className="text-3xl md:text-4xl font-bold text-primary-900 dark:text-primary-900 mb-2">
                {stat.number}
              </div>
              <div className="text-lg font-semibold text-gray-900 dark:text-white mb-1">
                {stat.label}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                {stat.description}
              </div>
            </div>
          ))}
        </div>

        {/* Benefits Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {benefits.map((benefit, index) => (
            <div
              key={index}
              className="group bg-white dark:bg-gray-700 rounded-xl p-6 border border-gray-200 dark:border-gray-600 hover:border-primary-300 dark:hover:border-secondary-600 transition-all duration-300 hover:shadow-lg transform hover:-translate-y-1"
            >
              <div className="bg-primary-900 p-3 rounded-lg inline-flex mb-4 group-hover:scale-110 transition-transform duration-300">
                <benefit.icon className="h-6 w-6 text-secondary-500" />
              </div>
              <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-3">
                {benefit.title}
              </h3>
              <p className="text-gray-600 dark:text-gray-300">
                {benefit.description}
              </p>
            </div>
          ))}
        </div>

        {/* Environmental Impact Section */}
        <div className="mt-16 bg-secondary-500 rounded-2xl p-8 md:p-12 text-center text-white">
          <h3 className="text-2xl md:text-3xl font-bold mb-4">
            Making a Real Environmental Impact
          </h3>
          <p className="text-lg mb-8 opacity-90 max-w-3xl mx-auto">
            Every trade on our platform contributes to a more sustainable future. Track your positive impact on the environment with real-time metrics.
          </p>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div>
              <div className="text-3xl font-bold mb-2">2,500 kg</div>
              <div className="opacity-90">CO₂ Saved Monthly</div>
            </div>
            <div>
              <div className="text-3xl font-bold mb-2">15,000 kWh</div>
              <div className="opacity-90">Clean Energy Traded</div>
            </div>
            <div>
              <div className="text-3xl font-bold mb-2">500+</div>
              <div className="opacity-90">Active Participants</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Benefits;