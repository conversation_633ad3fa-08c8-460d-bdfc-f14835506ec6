import React from 'react';
import { ArrowRight, Play } from 'lucide-react';

const Hero: React.FC = () => {
  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-primary-50 via-white to-secondary-50 dark:from-gray-900 dark:via-gray-800 dark:to-primary-900">
      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-primary-900/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-secondary-500/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-r from-primary-900/5 to-secondary-500/5 rounded-full blur-3xl animate-spin duration-20000"></div>
      </div>

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20">
        <div className="text-center">
          {/* Badge */}
          <div className="inline-flex items-center px-4 py-2 rounded-full bg-primary-100 dark:bg-primary-900/30 text-primary-800 dark:text-primary-300 text-sm font-medium mb-8 animate-fade-in">
            <span className="relative flex h-2 w-2 mr-2">
              <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-secondary-500 opacity-75"></span>
              <span className="relative inline-flex rounded-full h-2 w-2 bg-secondary-500"></span>
            </span>
            UAE's First Blockchain Energy Marketplace
          </div>

          {/* Main Headlines */}
          <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold text-gray-900 dark:text-white mb-6 animate-fade-in-up">
            <span className="block">Revolutionizing</span>
            <span className="block text-primary-900 dark:text-primary-900">
              Solar Energy Trading
            </span>
            <span className="block">with Blockchain</span>
          </h1>

          <p className="text-xl md:text-2xl text-gray-600 dark:text-gray-300 mb-8 max-w-3xl mx-auto animate-fade-in-up delay-200">
            Connect, Trade, and Earn in the UAE's First Peer-to-Peer Energy Marketplace
          </p>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12 animate-fade-in-up delay-400">
            <button className="group bg-primary-900 text-secondary-500 px-8 py-4 rounded-lg text-lg font-semibold hover:bg-primary-950 transition-all duration-200 transform hover:scale-105 shadow-lg">
              Join the Energy Revolution
              <ArrowRight className="inline-block ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform duration-200" />
            </button>
            <button className="group flex items-center px-8 py-4 border-2 border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg text-lg font-semibold hover:border-primary-900 dark:hover:border-primary-900 hover:text-primary-900 dark:hover:text-primary-900 transition-all duration-200">
              <Play className="mr-2 h-5 w-5" />
              Watch Demo
            </button>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto animate-fade-in-up delay-600">
            <div className="text-center">
              <div className="text-3xl md:text-4xl font-bold text-primary-900 dark:text-primary-900 mb-2">24/7</div>
              <div className="text-gray-600 dark:text-gray-400">Energy Trading</div>
            </div>
            <div className="text-center">
              <div className="text-3xl md:text-4xl font-bold text-primary-900 dark:text-primary-900 mb-2">100%</div>
              <div className="text-gray-600 dark:text-gray-400">Renewable Source</div>
            </div>
            <div className="text-center">
              <div className="text-3xl md:text-4xl font-bold text-primary-900 dark:text-primary-900 mb-2">0</div>
              <div className="text-gray-600 dark:text-gray-400">Carbon Footprint</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;