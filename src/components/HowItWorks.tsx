import React, { useState } from 'react';
import { Link, BarChart3, Coins } from 'lucide-react';

const HowItWorks: React.FC = () => {
  const [activeStep, setActiveStep] = useState(0);

  const steps = [
    {
      icon: Link,
      title: 'Connect Your Solar Assets',
      description: 'Register your solar panels and connect them to our blockchain platform. Our smart meters track energy production in real-time.',
      details: [
        'Smart meter integration',
        'Asset verification',
        'Production monitoring',
        'Blockchain registration',
      ],
    },
    {
      icon: BarChart3,
      title: 'Trade Energy Peer-to-Peer',
      description: 'Set your energy prices and let our AI-powered matching system connect you with buyers automatically.',
      details: [
        'Automated matching',
        'Dynamic pricing',
        'Real-time trading',
        'Market analytics',
      ],
    },
    {
      icon: Coins,
      title: 'Earn Through Smart Contracts',
      description: 'Receive instant payments through secure smart contracts. All transactions are transparent and immutable.',
      details: [
        'Instant settlements',
        'Smart contracts',
        'Transparent pricing',
        'Automated payments',
      ],
    },
  ];

  return (
    <section id="how-it-works" className="py-20 bg-gray-50 dark:bg-gray-800">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
            How It Works
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Three simple steps to start trading renewable energy and maximizing your solar investment
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Steps Navigation */}
          <div className="space-y-6">
            {steps.map((step, index) => (
              <div
                key={index}
                className={`relative cursor-pointer transition-all duration-300 ${
                  activeStep === index ? 'transform scale-105' : ''
                }`}
                onClick={() => setActiveStep(index)}
                onMouseEnter={() => setActiveStep(index)}
              >
                <div
                  className={`p-6 rounded-2xl border-2 transition-all duration-300 ${
                    activeStep === index
                      ? 'border-primary-900 bg-white dark:bg-gray-700 shadow-lg'
                      : 'border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-700 hover:border-primary-300 dark:hover:border-secondary-600'
                  }`}
                >
                  <div className="flex items-start space-x-4">
                    <div
                      className={`flex-shrink-0 w-12 h-12 rounded-lg flex items-center justify-center transition-all duration-300 ${
                        activeStep === index
                          ? 'bg-primary-900'
                          : 'bg-gray-100 dark:bg-gray-600'
                      }`}
                    >
                      <step.icon
                        className={`h-6 w-6 ${
                          activeStep === index ? 'text-secondary-500' : 'text-gray-600 dark:text-gray-300'
                        }`}
                      />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center mb-2">
                        <span
                          className={`text-sm font-semibold px-2 py-1 rounded-full mr-3 ${
                            activeStep === index
                              ? 'bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-300'
                              : 'bg-gray-100 text-gray-600 dark:bg-gray-600 dark:text-gray-300'
                          }`}
                        >
                          Step {index + 1}
                        </span>
                      </div>
                      <h3
                        className={`text-lg font-bold mb-2 ${
                          activeStep === index
                            ? 'text-gray-900 dark:text-white'
                            : 'text-gray-700 dark:text-gray-300'
                        }`}
                      >
                        {step.title}
                      </h3>
                      <p
                        className={`text-sm ${
                          activeStep === index
                            ? 'text-gray-600 dark:text-gray-300'
                            : 'text-gray-500 dark:text-gray-400'
                        }`}
                      >
                        {step.description}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Active Step Details */}
          <div className="bg-white dark:bg-gray-700 rounded-2xl p-8 shadow-lg border border-gray-200 dark:border-gray-600">
            <div className="mb-6">
              <div className="flex items-center mb-4">
                <div className="bg-primary-900 p-3 rounded-lg mr-4">
                  {React.createElement(steps[activeStep].icon, {
                    className: 'h-6 w-6 text-secondary-500',
                  })}
                </div>
                <div>
                  <span className="text-sm font-semibold text-primary-900 dark:text-primary-900">
                    Step {activeStep + 1}
                  </span>
                  <h3 className="text-xl font-bold text-gray-900 dark:text-white">
                    {steps[activeStep].title}
                  </h3>
                </div>
              </div>
              <p className="text-gray-600 dark:text-gray-300 mb-6">
                {steps[activeStep].description}
              </p>
            </div>

            <div className="space-y-3">
              <h4 className="font-semibold text-gray-900 dark:text-white mb-3">Key Features:</h4>
              {steps[activeStep].details.map((detail, idx) => (
                <div key={idx} className="flex items-center">
                  <div className="w-2 h-2 rounded-full bg-primary-900 mr-3"></div>
                  <span className="text-gray-700 dark:text-gray-300">{detail}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HowItWorks;