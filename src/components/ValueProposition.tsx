import React from 'react';
import { TrendingUp, Zap, Handshake } from 'lucide-react';

const ValueProposition: React.FC = () => {
  const propositions = [
    {
      icon: TrendingUp,
      title: 'For Producers',
      headline: 'Maximize Returns on Your Solar Investment',
      description: 'Sell excess energy directly to consumers at competitive rates. Our blockchain platform ensures transparent pricing and instant settlements.',
      benefits: ['Higher energy prices', 'Direct market access', 'Automated trading', 'Real-time analytics'],
      color: 'bg-primary-900',
    },
    {
      icon: Zap,
      title: 'For Consumers',
      headline: 'Access Clean Energy at Competitive Rates',
      description: 'Buy renewable energy directly from local producers. Track your energy source and reduce your carbon footprint.',
      benefits: ['Lower energy costs', 'Renewable guarantee', 'Energy traceability', 'Community impact'],
      color: 'bg-secondary-500',
    },
    {
      icon: Handshake,
      title: 'For Partners',
      headline: 'Join the Future of Energy Distribution',
      description: 'Partner with us to build the renewable energy ecosystem. Access cutting-edge blockchain technology and grow your business.',
      benefits: ['Technology access', 'Market expansion', 'Revenue sharing', 'Innovation support'],
      color: 'bg-secondary-500',
    },
  ];

  return (
    <section className="py-20 bg-white dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
            Powering the Future of Energy
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Our blockchain-powered marketplace creates value for every participant in the energy ecosystem
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {propositions.map((prop, index) => (
            <div
              key={index}
              className="group relative bg-white dark:bg-gray-800 rounded-2xl p-8 border border-gray-200 dark:border-gray-700 hover:border-primary-300 dark:hover:border-secondary-600 transition-all duration-300 hover:shadow-xl transform hover:-translate-y-2"
            >
              {/* Icon */}
              <div className={`inline-flex p-3 rounded-lg ${prop.color} mb-6`}>
                <prop.icon className="h-6 w-6 text-white" />
              </div>

              {/* Content */}
              <div className="mb-4">
                <span className="text-sm font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wide">
                  {prop.title}
                </span>
                <h3 className="text-xl font-bold text-gray-900 dark:text-white mt-2 mb-4">
                  {prop.headline}
                </h3>
                <p className="text-gray-600 dark:text-gray-300 mb-6">
                  {prop.description}
                </p>
              </div>

              {/* Benefits */}
              <ul className="space-y-3">
                {prop.benefits.map((benefit, idx) => (
                  <li key={idx} className="flex items-center text-gray-700 dark:text-gray-300">
                    <div className={`w-2 h-2 rounded-full ${prop.color} mr-3`}></div>
                    {benefit}
                  </li>
                ))}
              </ul>

              {/* Hover effect */}
              <div className={`absolute inset-0 rounded-2xl ${prop.color} opacity-0 group-hover:opacity-5 transition-opacity duration-300`}></div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default ValueProposition;