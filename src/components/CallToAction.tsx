import React, { useState } from 'react';
import { Mail, ArrowRight, CheckCircle } from 'lucide-react';
import { Contact } from '../types';

const CallToAction: React.FC = () => {
  const [email, setEmail] = useState('');
  const [userType, setUserType] = useState<'producer' | 'consumer' | 'partner'>('consumer');
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    setIsSubmitted(true);
    setIsLoading(false);
    setEmail('');
  };

  const userTypes = [
    { value: 'consumer', label: 'Energy Consumer', description: 'Looking to buy clean energy' },
    { value: 'producer', label: 'Energy Producer', description: 'Want to sell solar energy' },
    { value: 'partner', label: 'Business Partner', description: 'Interested in collaboration' },
  ];

  if (isSubmitted) {
    return (
      <section id="contact" className="py-20 bg-gradient-to-br from-primary-900 via-primary-800 to-secondary-500">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="bg-white/10 backdrop-blur-md rounded-2xl p-8 border border-white/20">
            <CheckCircle className="h-16 w-16 text-white mx-auto mb-6" />
            <h2 className="text-3xl font-bold text-white mb-4">
              Welcome to the Energy Revolution!
            </h2>
            <p className="text-xl text-white/90 mb-6">
              Thank you for joining our early access program. We'll notify you as soon as the platform launches.
            </p>
            <div className="bg-white/10 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-white mb-4">What happens next?</h3>
              <div className="space-y-3 text-white/90">
                <div className="flex items-center justify-center">
                  <div className="w-2 h-2 rounded-full bg-white mr-3"></div>
                  We'll send you platform updates and launch notifications
                </div>
                <div className="flex items-center justify-center">
                  <div className="w-2 h-2 rounded-full bg-white mr-3"></div>
                  Priority access to beta testing opportunities
                </div>
                <div className="flex items-center justify-center">
                  <div className="w-2 h-2 rounded-full bg-white mr-3"></div>
                  Exclusive early adopter benefits and discounts
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section id="contact" className="py-20 bg-secondary-500">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center text-white mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Be Among the First to Access the Platform
          </h2>
          <p className="text-xl opacity-90 max-w-2xl mx-auto">
            Join our early access program and start trading renewable energy when we launch. 
            Get exclusive benefits and help shape the future of energy.
          </p>
        </div>

        <div className="bg-white/10 backdrop-blur-md rounded-2xl p-8 border border-white/20">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* User Type Selection */}
            <div>
              <label className="block text-white font-semibold mb-4">I am a:</label>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {userTypes.map((type) => (
                  <label
                    key={type.value}
                    className={`cursor-pointer p-4 rounded-lg border-2 transition-all duration-200 ${
                      userType === type.value
                        ? 'border-white bg-white/20'
                        : 'border-white/30 bg-white/10 hover:bg-white/15'
                    }`}
                  >
                    <input
                      type="radio"
                      name="userType"
                      value={type.value}
                      checked={userType === type.value}
                      onChange={(e) => setUserType(e.target.value as typeof userType)}
                      className="sr-only"
                    />
                    <div className="text-white">
                      <div className="font-semibold mb-1">{type.label}</div>
                      <div className="text-sm opacity-80">{type.description}</div>
                    </div>
                  </label>
                ))}
              </div>
            </div>

            {/* Email Input */}
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1 relative">
                <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Enter your email address"
                  required
                  className="w-full pl-10 pr-4 py-3 rounded-lg bg-white/20 border border-white/30 text-white placeholder-white/70 focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-white/50"
                />
              </div>
              <button
                type="submit"
                disabled={isLoading}
                className="px-8 py-3 bg-white text-secondary-500 font-semibold rounded-lg hover:bg-gray-100 transition-all duration-200 transform hover:scale-105 disabled:opacity-70 disabled:cursor-not-allowed flex items-center justify-center"
              >
                {isLoading ? (
                  <div className="w-5 h-5 border-2 border-secondary-500 border-t-transparent rounded-full animate-spin"></div>
                ) : (
                  <>
                    Get Early Access
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </>
                )}
              </button>
            </div>
          </form>

          {/* Benefits */}
          <div className="mt-8 pt-8 border-t border-white/20">
            <h3 className="text-white font-semibold mb-4 text-center">Early Adopter Benefits:</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center text-white/90">
                <CheckCircle className="h-5 w-5 mr-3 text-white" />
                Priority platform access
              </div>
              <div className="flex items-center text-white/90">
                <CheckCircle className="h-5 w-5 mr-3 text-white" />
                Exclusive trading discounts
              </div>
              <div className="flex items-center text-white/90">
                <CheckCircle className="h-5 w-5 mr-3 text-white" />
                Beta testing opportunities
              </div>
              <div className="flex items-center text-white/90">
                <CheckCircle className="h-5 w-5 mr-3 text-white" />
                Direct support access
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default CallToAction;