import React from 'react';
import { <PERSON>, <PERSON>ap, <PERSON>Chart, Globe } from 'lucide-react';

const Technology: React.FC = () => {
  const features = [
    {
      icon: Shield,
      title: 'ICP Blockchain Security',
      description: 'Built on Internet Computer Protocol for maximum security and scalability',
      stats: '99.9% Uptime',
    },
    {
      icon: Zap,
      title: 'Real-time Tracking',
      description: 'Monitor energy production and consumption with live data updates',
      stats: '< 1s Latency',
    },
    {
      icon: Bar<PERSON>hart,
      title: 'Smart Analytics',
      description: 'AI-powered insights for optimal trading strategies and energy management',
      stats: '15% Avg. Savings',
    },
    {
      icon: Globe,
      title: 'Carbon Transparency',
      description: 'Track the environmental impact of every energy transaction',
      stats: '100% Traceable',
    },
  ];

  return (
    <section id="technology" className="py-20 bg-white dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
            Cutting-Edge Technology
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Powered by blockchain innovation and smart contract automation for a transparent, secure energy marketplace
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
          {features.map((feature, index) => (
            <div
              key={index}
              className="group text-center p-6 rounded-xl bg-gray-50 dark:bg-gray-800 hover:bg-white dark:hover:bg-gray-700 border border-gray-200 dark:border-gray-700 transition-all duration-300 hover:shadow-lg transform hover:-translate-y-1"
            >
              <div className="bg-gradient-to-r from-primary-900 to-secondary-500 p-3 rounded-lg inline-flex mb-4 group-hover:scale-110 transition-transform duration-300">
                <feature.icon className="h-6 w-6 text-secondary-500" />
              </div>
              <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-2">
                {feature.title}
              </h3>
              <p className="text-gray-600 dark:text-gray-300 text-sm mb-3">
                {feature.description}
              </p>
              <div className="text-primary-900 dark:text-primary-900 font-semibold text-sm">
                {feature.stats}
              </div>
            </div>
          ))}
        </div>

        {/* Technology Visualization */}
        <div className="bg-gray-50 dark:bg-gray-800 rounded-2xl p-8 md:p-12">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h3 className="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white mb-6">
                Blockchain-Powered Energy Trading
              </h3>
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 rounded-full bg-primary-900 mt-2"></div>
                  <div>
                    <h4 className="font-semibold text-gray-900 dark:text-white">Smart Contracts</h4>
                    <p className="text-gray-600 dark:text-gray-300 text-sm">
                      Automated execution of energy trades with instant settlements
                    </p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 rounded-full bg-secondary-500 mt-2"></div>
                  <div>
                    <h4 className="font-semibold text-gray-900 dark:text-white">Immutable Records</h4>
                    <p className="text-gray-600 dark:text-gray-300 text-sm">
                      All energy transactions permanently recorded on the blockchain
                    </p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 rounded-full bg-primary-900 mt-2"></div>
                  <div>
                    <h4 className="font-semibold text-gray-900 dark:text-white">Decentralized Network</h4>
                    <p className="text-gray-600 dark:text-gray-300 text-sm">
                      No single point of failure with distributed consensus
                    </p>
                  </div>
                </div>
              </div>
            </div>
            <div className="relative">
              <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-600">
                <div className="text-center mb-4">
                  <h4 className="font-bold text-gray-900 dark:text-white">Live Energy Flow</h4>
                </div>
                <div className="space-y-3">
                  <div className="flex justify-between items-center p-3 bg-primary-50 dark:bg-primary-900/20 rounded-lg">
                    <span className="text-sm text-gray-700 dark:text-gray-300">Solar Production</span>
                    <span className="font-semibold text-primary-900 dark:text-primary-900">2.5 kWh</span>
                  </div>
                  <div className="flex justify-between items-center p-3 bg-gray-100 dark:bg-secondary-500/20 rounded-lg">
                    <span className="text-sm text-gray-700 dark:text-gray-300">Market Price</span>
                    <span className="font-semibold text-secondary-500 dark:text-secondary-500">0.15 AED/kWh</span>
                  </div>
                  <div className="flex justify-between items-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                    <span className="text-sm text-gray-700 dark:text-gray-300">Active Trades</span>
                    <span className="font-semibold text-blue-600 dark:text-blue-400">847</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Technology;