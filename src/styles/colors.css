/* Energy Live Exchange Color System */
/* Primary: Deep Navy Blue (#002a35) | Secondary: Bright Yellow-Green (#e6ff2b) */

:root {
  /* PRIMARY COLORS - Deep Navy Blue Scale */
  --primary-50: #f0f9ff;   /* rgb(240, 249, 255) | hsl(204, 100%, 97%) */
  --primary-100: #e0f2fe;  /* rgb(224, 242, 254) | hsl(204, 96%, 94%) */
  --primary-200: #bae6fd;  /* rgb(186, 230, 253) | hsl(204, 94%, 86%) */
  --primary-300: #7dd3fc;  /* rgb(125, 211, 252) | hsl(204, 94%, 74%) */
  --primary-400: #38bdf8;  /* rgb(56, 189, 248) | hsl(204, 93%, 60%) */
  --primary-500: #0ea5e9;  /* rgb(14, 165, 233) | hsl(204, 89%, 48%) */
  --primary-600: #0284c7;  /* rgb(2, 132, 199) | hsl(204, 98%, 39%) */
  --primary-700: #0369a1;  /* rgb(3, 105, 161) | hsl(204, 96%, 32%) */
  --primary-800: #075985;  /* rgb(7, 89, 133) | hsl(204, 90%, 27%) */
  --primary-900: #002a35;  /* rgb(0, 42, 53) | hsl(192, 100%, 10%) - Main Primary */
  --primary-950: #001a21;  /* rgb(0, 26, 33) | hsl(193, 100%, 6%) */

  /* SECONDARY COLORS - Bright Yellow-Green Scale */
  --secondary-50: #fefce8;  /* rgb(254, 252, 232) | hsl(55, 92%, 95%) */
  --secondary-100: #fef9c3; /* rgb(254, 249, 195) | hsl(55, 96%, 88%) */
  --secondary-200: #fef08a; /* rgb(254, 240, 138) | hsl(53, 98%, 77%) */
  --secondary-300: #fde047; /* rgb(253, 224, 71) | hsl(50, 98%, 64%) */
  --secondary-400: #facc15; /* rgb(250, 204, 21) | hsl(48, 96%, 53%) */
  --secondary-500: #e6ff2b; /* rgb(230, 255, 43) | hsl(67, 100%, 58%) - Main Secondary */
  --secondary-600: #ca8a04; /* rgb(202, 138, 4) | hsl(41, 96%, 40%) */
  --secondary-700: #a16207; /* rgb(161, 98, 7) | hsl(36, 92%, 33%) */
  --secondary-800: #854d0e; /* rgb(133, 77, 14) | hsl(32, 81%, 29%) */
  --secondary-900: #713f12; /* rgb(113, 63, 18) | hsl(28, 73%, 26%) */
  --secondary-950: #422006; /* rgb(66, 32, 6) | hsl(26, 83%, 14%) */

  /* NEUTRAL COLORS - Balanced grays with slight blue undertone */
  --neutral-50: #f8fafc;   /* rgb(248, 250, 252) | hsl(210, 20%, 98%) */
  --neutral-100: #f1f5f9;  /* rgb(241, 245, 249) | hsl(210, 40%, 96%) */
  --neutral-200: #e2e8f0;  /* rgb(226, 232, 240) | hsl(214, 32%, 91%) */
  --neutral-300: #cbd5e1;  /* rgb(203, 213, 225) | hsl(215, 25%, 84%) */
  --neutral-400: #94a3b8;  /* rgb(148, 163, 184) | hsl(215, 20%, 65%) */
  --neutral-500: #64748b;  /* rgb(100, 116, 139) | hsl(215, 16%, 47%) */
  --neutral-600: #475569;  /* rgb(71, 85, 105) | hsl(215, 19%, 35%) */
  --neutral-700: #334155;  /* rgb(51, 65, 85) | hsl(215, 25%, 27%) */
  --neutral-800: #1e293b;  /* rgb(30, 41, 59) | hsl(217, 33%, 17%) */
  --neutral-900: #0f172a;  /* rgb(15, 23, 42) | hsl(222, 47%, 11%) */
  --neutral-950: #020617;  /* rgb(2, 6, 23) | hsl(222, 84%, 5%) */

  /* ACCENT COLORS - Complementary colors for variety */
  --accent-teal-400: #2dd4bf;    /* rgb(45, 212, 191) | hsl(172, 66%, 50%) */
  --accent-teal-500: #14b8a6;    /* rgb(20, 184, 166) | hsl(172, 80%, 40%) */
  --accent-emerald-400: #34d399; /* rgb(52, 211, 153) | hsl(156, 64%, 52%) */
  --accent-emerald-500: #10b981; /* rgb(16, 185, 129) | hsl(156, 84%, 39%) */
  --accent-orange-400: #fb923c;  /* rgb(251, 146, 60) | hsl(27, 96%, 61%) */
  --accent-orange-500: #f97316;  /* rgb(249, 115, 22) | hsl(25, 95%, 53%) */
  --accent-red-400: #f87171;     /* rgb(248, 113, 113) | hsl(0, 91%, 71%) */
  --accent-red-500: #ef4444;     /* rgb(239, 68, 68) | hsl(0, 84%, 60%) */

  /* SEMANTIC COLORS */
  --success-light: #dcfce7;  /* rgb(220, 252, 231) | hsl(138, 76%, 93%) */
  --success: #16a34a;        /* rgb(22, 163, 74) | hsl(142, 76%, 36%) */
  --success-dark: #15803d;   /* rgb(21, 128, 61) | hsl(142, 72%, 29%) */
  
  --warning-light: #fef3c7;  /* rgb(254, 243, 199) | hsl(48, 96%, 89%) */
  --warning: #f59e0b;        /* rgb(245, 158, 11) | hsl(38, 92%, 50%) */
  --warning-dark: #d97706;   /* rgb(217, 119, 6) | hsl(32, 95%, 44%) */
  
  --error-light: #fee2e2;    /* rgb(254, 226, 226) | hsl(0, 93%, 94%) */
  --error: #dc2626;          /* rgb(220, 38, 38) | hsl(0, 73%, 51%) */
  --error-dark: #b91c1c;     /* rgb(185, 28, 28) | hsl(0, 74%, 42%) */
  
  --info-light: #dbeafe;     /* rgb(219, 234, 254) | hsl(214, 95%, 93%) */
  --info: #3b82f6;           /* rgb(59, 130, 246) | hsl(217, 91%, 60%) */
  --info-dark: #1d4ed8;      /* rgb(29, 78, 216) | hsl(217, 78%, 48%) */
}

/* DARK MODE OVERRIDES */
.dark {
  --neutral-50: #020617;
  --neutral-100: #0f172a;
  --neutral-200: #1e293b;
  --neutral-300: #334155;
  --neutral-400: #475569;
  --neutral-500: #64748b;
  --neutral-600: #94a3b8;
  --neutral-700: #cbd5e1;
  --neutral-800: #e2e8f0;
  --neutral-900: #f1f5f9;
  --neutral-950: #f8fafc;
}

/* ACCESSIBILITY-COMPLIANT COLOR COMBINATIONS */

/* Text on Background Combinations (4.5:1+ contrast ratio) */
.text-on-primary {
  color: var(--neutral-50);
  background-color: var(--primary-900);
}

.text-on-secondary {
  color: var(--primary-900);
  background-color: var(--secondary-500);
}

.text-on-light {
  color: var(--primary-900);
  background-color: var(--neutral-50);
}

.text-on-dark {
  color: var(--neutral-50);
  background-color: var(--primary-900);
}

/* Call-to-Action Elements */
.cta-primary {
  background: linear-gradient(135deg, var(--primary-900), var(--primary-800));
  color: var(--neutral-50);
  border: 2px solid transparent;
}

.cta-primary:hover {
  background: linear-gradient(135deg, var(--primary-800), var(--primary-700));
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 42, 53, 0.3);
}

.cta-secondary {
  background: linear-gradient(135deg, var(--secondary-500), var(--secondary-400));
  color: var(--primary-900);
  border: 2px solid transparent;
}

.cta-secondary:hover {
  background: linear-gradient(135deg, var(--secondary-400), var(--secondary-300));
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(230, 255, 43, 0.4);
}

.cta-outline {
  background: transparent;
  color: var(--primary-900);
  border: 2px solid var(--primary-900);
}

.cta-outline:hover {
  background: var(--primary-900);
  color: var(--neutral-50);
}

/* Disabled States */
.disabled {
  background-color: var(--neutral-300);
  color: var(--neutral-500);
  cursor: not-allowed;
  opacity: 0.6;
}

.dark .disabled {
  background-color: var(--neutral-700);
  color: var(--neutral-400);
}

/* Interactive States */
.interactive:hover {
  background-color: var(--primary-50);
  border-color: var(--primary-300);
}

.dark .interactive:hover {
  background-color: var(--primary-900);
  border-color: var(--primary-700);
}

.interactive:focus {
  outline: 2px solid var(--secondary-500);
  outline-offset: 2px;
}

.interactive:active {
  transform: scale(0.98);
}

/* Status Indicators */
.status-success {
  background-color: var(--success-light);
  color: var(--success-dark);
  border-left: 4px solid var(--success);
}

.status-warning {
  background-color: var(--warning-light);
  color: var(--warning-dark);
  border-left: 4px solid var(--warning);
}

.status-error {
  background-color: var(--error-light);
  color: var(--error-dark);
  border-left: 4px solid var(--error);
}

.status-info {
  background-color: var(--info-light);
  color: var(--info-dark);
  border-left: 4px solid var(--info);
}

/* Gradient Backgrounds */
.gradient-primary {
  background: linear-gradient(135deg, var(--primary-900), var(--primary-700));
}

.gradient-secondary {
  background: linear-gradient(135deg, var(--secondary-500), var(--secondary-400));
}

.gradient-accent {
  background: linear-gradient(135deg, var(--primary-900), var(--accent-teal-500));
}

.gradient-energy {
  background: linear-gradient(135deg, var(--primary-900), var(--secondary-500));
}

/* Card and Surface Colors */
.surface-elevated {
  background-color: var(--neutral-50);
  border: 1px solid var(--neutral-200);
  box-shadow: 0 4px 6px rgba(0, 42, 53, 0.1);
}

.dark .surface-elevated {
  background-color: var(--neutral-800);
  border: 1px solid var(--neutral-700);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
}

.surface-elevated:hover {
  box-shadow: 0 10px 25px rgba(0, 42, 53, 0.15);
  border-color: var(--primary-300);
}

.dark .surface-elevated:hover {
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.4);
  border-color: var(--primary-700);
}