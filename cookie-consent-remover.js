/**
 * <PERSON><PERSON> Consent Banner Removal Solution
 * 
 * This script provides multiple methods to identify and remove persistent
 * cookie consent banners when the Accept button is not functioning properly.
 */

// Method 1: Common Cookie Banner Selectors
const COOKIE_BANNER_SELECTORS = [
  // Generic selectors
  '[id*="cookie"]',
  '[class*="cookie"]',
  '[id*="consent"]',
  '[class*="consent"]',
  '[id*="gdpr"]',
  '[class*="gdpr"]',
  '[id*="privacy"]',
  '[class*="privacy"]',
  
  // Common framework selectors
  '.cookie-banner',
  '.cookie-notice',
  '.cookie-bar',
  '.consent-banner',
  '.privacy-banner',
  '.gdpr-banner',
  '#cookie-banner',
  '#cookie-notice',
  '#cookie-bar',
  '#consent-banner',
  '#privacy-banner',
  '#gdpr-banner',
  
  // Popular cookie consent libraries
  '.cc-banner', // CookieConsent
  '.cc-window',
  '.cookiesjsr-banner',
  '.optanon-alert-box-wrapper', // OneTrust
  '.ot-sdk-container',
  '.cky-consent-container', // CookieYes
  '.cookie-law-info-bar', // GDPR Cookie Consent
  '.cli-bar-container',
  '.moove_gdpr_cookie_info_bar', // GDPR Cookie Compliance
  '.cmplz-cookiebanner', // Complianz
  '.pea_cook_wrapper', // Cookie Notice & Compliance
  '.cookie_action_close_header',
  
  // Fixed position elements that might be banners
  '[style*="position: fixed"]',
  '[style*="position:fixed"]'
];

// Method 2: Text-based detection
const COOKIE_BANNER_TEXT_PATTERNS = [
  /cookie/i,
  /consent/i,
  /privacy/i,
  /gdpr/i,
  /accept/i,
  /decline/i,
  /necessary cookies/i,
  /we use cookies/i,
  /this website uses/i
];

// Method 3: Overlay and backdrop selectors
const OVERLAY_SELECTORS = [
  '.modal-backdrop',
  '.overlay',
  '.backdrop',
  '[class*="overlay"]',
  '[class*="backdrop"]',
  '[style*="z-index"]'
];

/**
 * Main function to remove cookie consent banners
 */
function removeCookieConsentBanners() {
  console.log('🍪 Starting cookie consent banner removal...');
  
  let removedCount = 0;
  
  // Method 1: Remove by common selectors
  COOKIE_BANNER_SELECTORS.forEach(selector => {
    try {
      const elements = document.querySelectorAll(selector);
      elements.forEach(element => {
        if (isCookieBanner(element)) {
          removeElement(element);
          removedCount++;
        }
      });
    } catch (e) {
      console.warn(`Error with selector ${selector}:`, e);
    }
  });
  
  // Method 2: Remove by text content analysis
  const allElements = document.querySelectorAll('*');
  allElements.forEach(element => {
    if (isCookieBannerByText(element)) {
      removeElement(element);
      removedCount++;
    }
  });
  
  // Method 3: Remove overlays that might be blocking interaction
  removeBlockingOverlays();
  
  // Method 4: Reset body overflow (in case it was disabled)
  document.body.style.overflow = '';
  document.documentElement.style.overflow = '';
  
  console.log(`✅ Removed ${removedCount} cookie consent elements`);
  
  return removedCount;
}

/**
 * Check if an element is likely a cookie banner
 */
function isCookieBanner(element) {
  if (!element || !element.textContent) return false;
  
  const text = element.textContent.toLowerCase();
  const hasKeywords = COOKIE_BANNER_TEXT_PATTERNS.some(pattern => pattern.test(text));
  
  // Additional checks
  const hasAcceptButton = element.querySelector('[class*="accept"], [id*="accept"], button[class*="accept"]');
  const hasDeclineButton = element.querySelector('[class*="decline"], [id*="decline"], button[class*="decline"]');
  const isFixedPosition = window.getComputedStyle(element).position === 'fixed';
  const isHighZIndex = parseInt(window.getComputedStyle(element).zIndex) > 1000;
  
  return hasKeywords && (hasAcceptButton || hasDeclineButton || isFixedPosition || isHighZIndex);
}

/**
 * Check if an element is a cookie banner based on text content
 */
function isCookieBannerByText(element) {
  if (!element || !element.textContent) return false;
  
  const text = element.textContent.toLowerCase();
  const wordCount = text.split(' ').length;
  
  // Must contain cookie-related keywords and be reasonably sized
  const hasCookieKeywords = /cookie|consent|privacy|gdpr/i.test(text);
  const hasActionWords = /accept|decline|agree|continue|ok|got it/i.test(text);
  const isReasonableSize = wordCount > 5 && wordCount < 200;
  
  return hasCookieKeywords && hasActionWords && isReasonableSize;
}

/**
 * Remove blocking overlays
 */
function removeBlockingOverlays() {
  OVERLAY_SELECTORS.forEach(selector => {
    const overlays = document.querySelectorAll(selector);
    overlays.forEach(overlay => {
      const style = window.getComputedStyle(overlay);
      if (style.position === 'fixed' && parseInt(style.zIndex) > 1000) {
        removeElement(overlay);
      }
    });
  });
}

/**
 * Safely remove an element
 */
function removeElement(element) {
  try {
    if (element && element.parentNode) {
      console.log('🗑️ Removing element:', element.className || element.id || element.tagName);
      element.remove();
    }
  } catch (e) {
    console.warn('Error removing element:', e);
  }
}

/**
 * Set up persistent monitoring for dynamically loaded banners
 */
function setupPersistentRemoval() {
  // Run immediately
  removeCookieConsentBanners();
  
  // Set up mutation observer for dynamically added content
  const observer = new MutationObserver((mutations) => {
    let shouldCheck = false;
    
    mutations.forEach((mutation) => {
      if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
        shouldCheck = true;
      }
    });
    
    if (shouldCheck) {
      setTimeout(removeCookieConsentBanners, 100);
    }
  });
  
  observer.observe(document.body, {
    childList: true,
    subtree: true
  });
  
  // Also run periodically as a fallback
  setInterval(removeCookieConsentBanners, 2000);
  
  console.log('🔄 Persistent cookie banner removal activated');
}

/**
 * Manual removal function for specific elements
 */
function manualRemoval(selector) {
  const elements = document.querySelectorAll(selector);
  elements.forEach(removeElement);
  console.log(`Manually removed ${elements.length} elements matching: ${selector}`);
}

/**
 * Force accept cookies by clicking accept buttons
 */
function forceAcceptCookies() {
  const acceptSelectors = [
    '[class*="accept"]',
    '[id*="accept"]',
    'button[class*="accept"]',
    'button[id*="accept"]',
    '[data-accept]',
    '[onclick*="accept"]',
    'button:contains("Accept")',
    'button:contains("OK")',
    'button:contains("Got it")',
    'button:contains("Continue")',
    'button:contains("Agree")'
  ];
  
  acceptSelectors.forEach(selector => {
    try {
      const buttons = document.querySelectorAll(selector);
      buttons.forEach(button => {
        if (button.textContent && /accept|ok|got it|continue|agree/i.test(button.textContent)) {
          button.click();
          console.log('🖱️ Clicked accept button:', button);
        }
      });
    } catch (e) {
      console.warn(`Error with accept selector ${selector}:`, e);
    }
  });
}

// Export functions for manual use
window.cookieConsentRemover = {
  remove: removeCookieConsentBanners,
  setupPersistent: setupPersistentRemoval,
  manualRemoval: manualRemoval,
  forceAccept: forceAcceptCookies
};

// Auto-start if script is loaded
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', setupPersistentRemoval);
} else {
  setupPersistentRemoval();
}