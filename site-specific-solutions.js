/**
 * Site-Specific Cookie Consent Removal Solutions
 * 
 * Tailored solutions for popular websites and cookie consent libraries
 */

const SITE_SPECIFIC_SOLUTIONS = {
  // YouTube
  'youtube.com': () => {
    document.querySelectorAll('tp-yt-paper-dialog, [role="dialog"]').forEach(el => {
      if (el.textContent && /cookie|consent/i.test(el.textContent)) {
        el.remove();
      }
    });
  },
  
  // Facebook
  'facebook.com': () => {
    document.querySelectorAll('[data-testid*="cookie"], [aria-label*="cookie"]').forEach(el => el.remove());
  },
  
  // Twitter/X
  'twitter.com': () => {
    document.querySelectorAll('[data-testid="sheetDialog"]').forEach(el => {
      if (el.textContent && /cookie|privacy/i.test(el.textContent)) {
        el.remove();
      }
    });
  },
  
  // LinkedIn
  'linkedin.com': () => {
    document.querySelectorAll('.artdeco-global-alert, [data-test-id*="cookie"]').forEach(el => el.remove());
  },
  
  // Reddit
  'reddit.com': () => {
    document.querySelectorAll('[class*="CookieBanner"], [data-testid*="cookie"]').forEach(el => el.remove());
  },
  
  // Instagram
  'instagram.com': () => {
    document.querySelectorAll('[role="dialog"]').forEach(el => {
      if (el.textContent && /cookie|data policy/i.test(el.textContent)) {
        el.remove();
      }
    });
  },
  
  // OneTrust (popular consent management platform)
  'onetrust': () => {
    document.querySelectorAll('#onetrust-consent-sdk, .optanon-alert-box-wrapper, .ot-sdk-container').forEach(el => el.remove());
  },
  
  // CookieBot
  'cookiebot': () => {
    document.querySelectorAll('#CybotCookiebotDialog, .CybotCookiebotDialog').forEach(el => el.remove());
  },
  
  // Quantcast Choice
  'quantcast': () => {
    document.querySelectorAll('#qc-cmp2-ui, .qc-cmp2-container').forEach(el => el.remove());
  }
};

/**
 * Apply site-specific solution
 */
function applySiteSpecificSolution() {
  const hostname = window.location.hostname;
  
  // Check for exact domain matches
  for (const [domain, solution] of Object.entries(SITE_SPECIFIC_SOLUTIONS)) {
    if (hostname.includes(domain)) {
      console.log(`Applying site-specific solution for ${domain}`);
      solution();
      return true;
    }
  }
  
  return false;
}

// Auto-apply site-specific solutions
applySiteSpecificSolution();